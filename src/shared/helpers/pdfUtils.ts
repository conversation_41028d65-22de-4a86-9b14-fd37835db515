/**
 * Utility functions for PDF generation with proper image loading
 */

/**
 * Waits for all images in an HTML element to load before resolving
 * This ensures that images are properly rendered in PDF generation
 */
export const waitForImagesToLoad = (element: HTMLElement): Promise<void> => {
  return new Promise((resolve) => {
    const images = element.querySelectorAll('img')
    const imagePromises: Promise<void>[] = []

    images.forEach((img) => {
      if (img.complete) {
        return // Image already loaded
      }

      const promise = new Promise<void>((resolveImg) => {
        const handleLoad = () => {
          img.removeEventListener('load', handleLoad)
          img.removeEventListener('error', handleError)
          resolveImg()
        }

        const handleError = () => {
          img.removeEventListener('load', handleLoad)
          img.removeEventListener('error', handleError)
          console.warn('Image failed to load:', img.src)
          resolveImg() // Resolve anyway to not block PDF generation
        }

        img.addEventListener('load', handleLoad)
        img.addEventListener('error', handleError)

        // Force reload if src is set but image hasn't loaded
        if (img.src && !img.complete) {
          const currentSrc = img.src
          img.src = ''
          img.src = currentSrc
        }
      })

      imagePromises.push(promise)
    })

    if (imagePromises.length === 0) {
      resolve()
    } else {
      console.log('imagePromises===[log]===>', imagePromises)
      Promise.all(imagePromises).then(() => resolve())
    }
  })
}

/**
 * Prepares an HTML element for PDF generation by:
 * 1. Adding it to the DOM temporarily (required for image loading)
 * 2. Waiting for all images to load
 * 3. Adding a small delay for rendering completion
 */
export const prepareElementForPDF = async (element: HTMLElement): Promise<void> => {
  // Append element to body temporarily to ensure images can load
  element.style.position = 'absolute'
  element.style.left = '-9999px'
  element.style.top = '-9999px'
  document.body.appendChild(element)

  try {
    // Wait for all images to load before generating PDF
    await waitForImagesToLoad(element)

    // Add a small delay to ensure rendering is complete
    await new Promise((resolve) => setTimeout(resolve, 500))
  } catch (error) {
    console.error('Error preparing element for PDF:', error)
    throw error
  }
}

/**
 * Cleans up a temporary DOM element
 */
export const cleanupTempElement = (element: HTMLElement): void => {
  if (element && element.parentNode) {
    element.parentNode.removeChild(element)
  }
}

/**
 * Default html2pdf configuration optimized for images
 */
export const getOptimizedPDFConfig = (filename: string = 'document.pdf') => ({
  margin: 8.5,
  filename,
  image: { type: 'jpeg', quality: 0.8 },
  html2canvas: {
    scale: 2,
    useCORS: true,
    allowTaint: true,
    logging: false,
    imageTimeout: 0,
  },
  jsPDF: {
    unit: 'mm',
    format: 'letter',
    orientation: 'portrait',
  },
  pagebreak: { mode: ['avoid-all', 'css'] },
})

/**
 * Enhanced CSS styles for PDF generation with proper image handling
 */
export const getPDFStyles = () => `
  <style>
    * { word-wrap: break-word; }
    table, div { page-break-inside: avoid; }
    p { page-break-inside: avoid; word-break: break-word; }
    
    /* Ensure sections don't split */
    .avoid-break {
      page-break-before: always; 
      page-break-inside: avoid;
    }
    
    /* Ensure images are properly sized and visible */
    img {
      max-width: 100% !important;
      height: auto !important;
      display: block !important;
    }
    
    /* Prevent content overflow */
    .no-break {
      break-inside: avoid;
      page-break-inside: avoid;
      overflow: hidden;
    }
  </style>
`
