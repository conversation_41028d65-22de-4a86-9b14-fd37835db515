import { useState, useEffect, useRef } from 'react'
import { useParams, useLocation, useNavigate } from 'react-router-dom'
import * as Styled from './photoReport.style'
import { dayjsFormat, generateUUID, isSuccess, notify } from '../../../shared/helpers/util'
import Button from '../../../shared/components/button/Button'
import { useSelector } from 'react-redux'
import Logo from '../../../assets/images/nhr.png'
// @ts-ignore
import html2pdf from 'html2pdf.js'
import ReactDOMServer from 'react-dom/server'
import PhotoLayout from './PhotoLayout'
import { createMediaOpportunity, getPresignedUrlMedia } from '../../../logic/apis/media'
import { FilePathTypeEnum } from '../../../shared/helpers/constants'
import { getThumbnailUrl } from '../mediaUtils'
import { prepareElementForPDF } from '../../../shared/helpers/pdfUtils'

const PhotoReport = () => {
  const [pages, setPages] = useState<any[][]>([])

  const navigate = useNavigate()
  const [captions, setCaptions] = useState<Record<string, string>>({})
  const [reportTitle, setReportTitle] = useState('Photo Report')
  const [reportDate, setReportDate] = useState(dayjsFormat(new Date(), 'YYYY-MM-DD'))
  const [loading, setLoading] = useState(false)
  const { oppId } = useParams()

  const globalSelector = useSelector((state: any) => state)
  const { currentMember } = globalSelector.company
  const location = useLocation()
  const imageData = location?.state?.imageData
  const oppData = location?.state?.oppData
  const handlePrint = async () => {
    setLoading(true)

    const htmlContent = ReactDOMServer.renderToString(
      <PhotoLayout
        oppData={oppData}
        pages={pages}
        createdBy={currentMember?.name}
        isPrintView
        captions={captions}
        reportTitle={reportTitle}
        reportDate={reportDate}
      />
    )

    const element = document.createElement('div')
    element.innerHTML = `
          <style>
          * { word-wrap: break-word; }
          table, div { page-break-inside: avoid; }
          p { page-break-inside: avoid; word-break: break-word; }
          
          /* Ensure sections don’t split */
          .avoid-break {
            page-break-before: always; 
            page-break-inside: avoid;
          }
        </style>
        ${htmlContent}
      `

    element.innerHTML = htmlContent

    try {
      await prepareElementForPDF(element)
      const pdfBlob = await html2pdf()
        .set({
          margin: 8.5,
          filename: `photo-report-${oppData?.PO || 'document'}.pdf`,
          image: { type: 'jpeg', quality: 0.2 },
          html2canvas: {
            scale: 2,
            useCORS: true,
            imageTimeout: 0,
          },
          jsPDF: {
            unit: 'mm',
            format: 'letter',
            orientation: 'portrait',
          },
          pagebreak: { mode: ['avoid-all', 'css'] },
        })
        .from(element)
        .outputPdf('blob')

      const fileName = `photo-report-${oppData?.PO || 'document'}.pdf`
      const presignedRes = await getPresignedUrlMedia(
        FilePathTypeEnum.Project,
        [{ fileName, mimetype: 'application/pdf' }],
        currentMember?._id,
        oppId!
      )

      if (!isSuccess(presignedRes)) {
        console.error('Error getting presigned URL:', presignedRes)
        return
      }

      const { url } = presignedRes.data.data?.signedUrls[0] // API returns URL & final file URL

      const uploadRes = await fetch(url, {
        method: 'PUT',
        body: pdfBlob,
        headers: { 'Content-Type': 'application/pdf' },
      })

      if (!uploadRes.ok) {
        console.error('Error uploading PDF:', uploadRes)
        return
      }

      if (url) {
        const payload = {
          _id: generateUUID()!,
          createdAt: new Date().toISOString(),
          createdBy: currentMember?._id,
          mimetype: 'application/pdf',
          name: fileName,
          url: url?.split('?')[0],
          thumbnail: getThumbnailUrl(url?.split('?')[0]),
          tags: ['Photo Report'],
        }

        const createRes = await createMediaOpportunity(oppId!, [payload])

        if (isSuccess(createRes)) {
          notify('Photo Report created successfully!', 'success')
          navigate(-1)
        }
      }

      setLoading(false)
    } catch (error) {
      console.error('Error generating PDF:', error)

      setLoading(false)
    }
  }

  useEffect(() => {
    if (imageData?.length) {
      const initialCaptions: Record<string, string> = {}
      imageData.forEach((item: any) => {
        initialCaptions[item._id] = item.description || ''
      })
      setCaptions(initialCaptions)

      const pages = []
      for (let i = 0; i < imageData.length; i += 4) {
        pages.push(imageData.slice(i, i + 4))
      }
      setPages(pages)
    }
  }, [imageData])

  return (
    <Styled.Container>
      <Styled.ActionBar>
        <Button width="max-content" className="gray" onClick={() => window.history.back()}>
          Cancel
        </Button>
        <Button width="max-content" onClick={handlePrint} isLoading={loading}>
          Create
        </Button>
      </Styled.ActionBar>
      <PhotoLayout
        oppData={oppData}
        pages={pages}
        createdBy={currentMember?.name}
        onCaptionChange={(mediaId, value) => {
          setCaptions((prev) => ({
            ...prev,
            [mediaId]: value,
          }))
        }}
        captions={captions}
        reportTitle={reportTitle}
        reportDate={reportDate}
        onTitleChange={setReportTitle}
        onDateChange={setReportDate}
      />
    </Styled.Container>
  )
}

export default PhotoReport
